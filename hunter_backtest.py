# hunter_backtest.py

import pandas as pd
import numpy as np
import os
import re
import sys
from datetime import datetime, time, timedelta
from tabulate import tabulate
import warnings

# --- 导入盈亏计算模块 ---
from profit_loss_calculator import batch_calculate_profit_loss, format_profit_loss_display

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# ——— 1. 配置区域 ———
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'  # <--- 请确保这是你的数据根目录!
BACKTEST_DATE = '2025-07-25'  # <--- 在这里修改你想要复盘的日期!
ENABLE_PROFIT_LOSS_CALCULATION = True  # <--- 设置为 True 开启盈亏计算, False 关闭

# --- “三位一体猎杀模型” 核心参数 ---
GAP_SIGNAL_SCORE_THRESHOLD = 1.60  # 触发“资金断层”的最低综合得分
GAP_SIGNAL_TIMEOUT_MINUTES = 30  # “资金断层”信号的有效时间（分钟）
LEADER_STOCKS_TO_BUY = 2  # 触发信号后，买入板块内的前N名龙头

# --- 全局状态变量 ---
previous_data_snapshot = {}
all_buy_signals = []  # 存储所有触发的买入信号
processed_buy_signals = set()  # 存储已触发买入的股票代码，防止重复买入
gap_signal_tracker = {}  # 追踪“资金断层”信号的状态 {'板块名': {'time': datetime, 'triggered': bool}}


class LogCapture:
    def __init__(self, log_file_path):
        self.log_file_path = log_file_path
        self.original_stdout = sys.stdout
        self.log_file = None

    def __enter__(self):
        self.log_file = open(self.log_file_path, 'w', encoding='utf-8')
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout
        if self.log_file:
            self.log_file.close()

    def write(self, text):
        self.original_stdout.write(text)
        if self.log_file:
            self.log_file.write(text)
            self.log_file.flush()

    def flush(self):
        self.original_stdout.flush()
        if self.log_file:
            self.log_file.flush()


def format_amount(amount):
    if amount is None or not isinstance(amount, (int, float)): return 'N/A'
    if abs(amount) >= 1e8: return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def convert_to_float(value):
    if isinstance(value, str):
        try:
            return float(value.replace('%', '').replace(',', ''))
        except (ValueError, TypeError):
            return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0


def extract_timestamp_from_filename(filename):
    # 优先匹配长格式，再匹配短格式
    match = re.search(r'_\d{8}_(\d{6})\.', filename) or \
            re.search(r'_(\d{6})\.csv$', filename) or \
            re.search(r'^(\d{2}-\d{2})_', filename)
    if match:
        # 处理 HH-MM 格式
        if '-' in match.group(1):
            return match.group(1).replace('-', '') + "00"
        # 处理 HHMMSS 格式
        return match.groups()[-1]
    return None


def classify_file_type(filename):
    if any(p in filename for p in ['fund_flow_rank_', 'ths_fund_flow.csv']): return 'stock_flow'
    if any(p in filename for p in ['concept_fund_flow_']): return 'concept'
    if any(p in filename for p in ['sector_fund_flow']): return 'sector'
    return 'other'


def find_latest_file(file_list, current_time):
    relevant_file = None
    best_timestamp = None
    for f in sorted(file_list):
        try:
            timestamp = extract_timestamp_from_filename(f)
            if not timestamp: continue
            f_ts = datetime.strptime(timestamp, '%H%M%S').time()
            if f_ts <= current_time:
                if best_timestamp is None or f_ts >= best_timestamp:
                    relevant_file = f
                    best_timestamp = f_ts
        except (ValueError, IndexError):
            continue
    return relevant_file


# --- 以下是V6/V7版本中的核心分析函数，保持原样 ---

def analyze_market_competition(inflows, names):
    if len(inflows) < 2: return {"is_competitive": False, "competition_type": "insufficient_data"}
    top_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')
    if top_ratio >= 1.20: return {"is_competitive": False, "competition_type": "clear_leader"}
    if len(inflows) >= 3 and all(inflows[i] / inflows[i + 1] < 1.15 for i in range(2)):
        return {"is_competitive": True, "competition_type": "三足鼎立", "leading_group": names[:3],
                "max_ratio": max(inflows[0] / inflows[1], inflows[1] / inflows[2])}
    if top_ratio < 1.10:
        return {"is_competitive": True, "competition_type": "双强争霸", "leading_group": names[:2],
                "max_ratio": top_ratio}
    return {"is_competitive": True, "competition_type": "竞争激烈", "leading_group": names[:2], "max_ratio": top_ratio}


def analyze_market_state(inflows):
    inflows_yi = [x / 1e8 for x in inflows[:5]]
    total_top5 = sum(inflows_yi)
    if total_top5 > 80:
        scale = "超大资金"
    elif total_top5 > 40:
        scale = "大资金"
    elif total_top5 > 15:
        scale = "中等资金"
    else:
        scale = "小资金"
    concentration = sum(inflows_yi[:2]) / total_top5 if total_top5 > 0 else 0
    dispersion = np.std(inflows_yi) / np.mean(inflows_yi) if np.mean(inflows_yi) > 0 else 0
    return {"scale": scale, "concentration": concentration, "dispersion": dispersion, "total_top5": total_top5,
            "avg_top5": np.mean(inflows_yi)}


def find_all_gap_points(inflows):
    gap_scores = []
    for i in range(min(5, len(inflows) - 1)):
        abs_gap = inflows[i] - inflows[i + 1]
        rel_gap = inflows[i] / inflows[i + 1] if inflows[i + 1] > 0 else float('inf')
        position_weight = 1.0 / (i + 1)
        gap_score = (abs_gap / 1e8) * rel_gap * position_weight
        gap_scores.append({"position": i, "abs_gap": abs_gap, "rel_gap": rel_gap, "score": gap_score})
    return max(gap_scores, key=lambda x: x["score"])


def identify_leading_group(inflows, gap_position, names):
    leading_group = inflows[:gap_position + 1]
    following_group = inflows[gap_position + 1:]
    internal_gaps = [leading_group[i] / leading_group[i + 1] for i in range(len(leading_group) - 1)] if len(
        leading_group) > 1 else []
    return {"size": len(leading_group), "members": names[:gap_position + 1],
            "internal_cohesion": max(internal_gaps) if internal_gaps else 1.0,
            "external_gap": leading_group[-1] / following_group[0] if following_group else float('inf')}


def calculate_dynamic_thresholds(market_state):
    scale_factors = {"超大资金": 1.2, "大资金": 1.3, "中等资金": 1.4, "小资金": 1.5}
    concentration_adjustment = (1 - market_state["concentration"]) * 0.1
    dynamic_relative_threshold = scale_factors[market_state["scale"]] + concentration_adjustment
    min_abs_gap_ratio = 0.15 + market_state["dispersion"] * 0.1
    dynamic_absolute_threshold = market_state["avg_top5"] * min_abs_gap_ratio
    return {"min_relative_gap": dynamic_relative_threshold, "min_absolute_gap": dynamic_absolute_threshold,
            "group_cohesion_limit": 1.4, "min_gap_score": 1.5}


def comprehensive_evaluation(market_state, max_gap, group_info, thresholds):
    rel_score = min(max_gap["rel_gap"] / thresholds["min_relative_gap"], 2.0)
    abs_score = min((max_gap["abs_gap"] / 1e8) / thresholds["min_absolute_gap"], 2.0)
    cohesion_score = 2.0 - (group_info["internal_cohesion"] - 1.0) / (thresholds["group_cohesion_limit"] - 1.0) if \
    group_info["internal_cohesion"] <= thresholds["group_cohesion_limit"] else 0
    concentration = market_state["concentration"]
    pattern_score = 1.8 if concentration > 0.65 else (1.2 if concentration > 0.45 else 0.6)
    position_score = 2.0 * (1 / (max_gap["position"] + 1))
    weights = {"relative": 0.25, "absolute": 0.25, "cohesion": 0.15, "pattern": 0.15, "position": 0.20}
    total_score = sum(
        [rel_score * weights["relative"], abs_score * weights["absolute"], cohesion_score * weights["cohesion"],
         pattern_score * weights["pattern"], position_score * weights["position"]])
    return total_score


def analyze_funding_gap(df_sectors, sector_type):
    positive_flow_df = df_sectors[df_sectors['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < 4: return {"is_gap": False, "report": f"【--- {sector_type}板块数据不足，无法分析 ---】"}
    inflows = positive_flow_df.head(10)['今日主力净流入-净额'].tolist()
    names = positive_flow_df.head(10)['名称'].tolist()
    competition_info = analyze_market_competition(inflows, names)
    if competition_info["is_competitive"]:
        return {"is_gap": False,
                "report": f"【--- {sector_type}板块{competition_info['competition_type']}，无明显龙头 ---】"}
    market_state = analyze_market_state(inflows)
    max_gap = find_all_gap_points(inflows)
    group_info = identify_leading_group(inflows, max_gap["position"], names)
    thresholds = calculate_dynamic_thresholds(market_state)
    total_score = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)
    is_gap_found = total_score >= thresholds["min_gap_score"]
    report = f"【★★★★★ {sector_type}板块发现资金断层! ★★★★★】\n  格局类型: {'单龙头断层' if group_info['size'] == 1 else f'Top {group_info['size']} 领先集团'}\n  断层龙头: 【{', '.join(group_info['members'])}】\n  断层分析: 在第{max_gap['position'] + 1}名后发现显著断层，相对差距{max_gap['rel_gap']:.2f}倍\n  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)" if is_gap_found else f"【--- {sector_type}板块未发现显著资金断层 ---】"
    return {"is_gap": is_gap_found, "report": report, "score": total_score, "leaders": group_info["members"]}


def analyze_acceleration(df_current, df_previous):
    if df_previous.empty: return []
    merged_df = pd.merge(df_current, df_previous, on='名称', suffixes=('_curr', '_prev'))
    merged_df['delta'] = merged_df['今日主力净流入-净额_curr'] - merged_df['今日主力净流入-净额_prev']
    merged_df['ratio'] = merged_df['今日主力净流入-净额_curr'] / (merged_df['今日主力净流入-净额_prev'] + 1e-6)
    accelerating = merged_df[
        (merged_df['delta'] >= 1.5e8) & (merged_df['ratio'] >= 1.3) & (merged_df['今日主力净流入-净额_curr'] >= 3e8)]
    return accelerating.to_dict('records')


def analyze_sector_internal_flow(sector_name, current_time, data_dir):
    safe_sector_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
    sector_summary_files = [f for f in os.listdir(data_dir) if
                            f.startswith(f"sector_summary_{safe_sector_name}_") and f.endswith('.csv')]
    latest_summary_file = find_latest_file(sector_summary_files, current_time)
    if latest_summary_file:
        try:
            df_internal = pd.read_csv(os.path.join(data_dir, latest_summary_file), encoding='utf-8-sig')
            if '代码' not in df_internal.columns:  # 兼容没有代码列的文件
                df_internal['代码'] = 'N/A'
            df_internal['代码'] = df_internal['代码'].astype(str).str.zfill(6)
            return df_internal.head(LEADER_STOCKS_TO_BUY).to_dict('records')
        except Exception as e:
            print(f"解析板块内部文件 {latest_summary_file} 失败: {e}")
    return []


def run_hunter_backtest(date_str):
    global previous_data_snapshot, all_buy_signals, processed_buy_signals, gap_signal_tracker

    log_file_path = f"hunter_backtest_log_{date_str}.txt"
    with LogCapture(log_file_path):
        print(f"--- 开始对日期 {date_str} 进行“三位一体猎杀模型”回测 (V1.1 修复版) ---")
        data_dir = os.path.join(BASE_DATA_DIR, date_str)
        if not os.path.isdir(data_dir):
            print(f"错误: 目录不存在 {data_dir}");
            return

        all_files = os.listdir(data_dir)
        industry_files = sorted([f for f in all_files if classify_file_type(f) == 'sector'])
        concept_files = sorted([f for f in all_files if classify_file_type(f) == 'concept'])
        stock_flow_files = sorted([f for f in all_files if classify_file_type(f) == 'stock_flow'])

        timestamps = sorted(
            list(set(extract_timestamp_from_filename(f) for f in all_files if extract_timestamp_from_filename(f))))

        for ts_str in timestamps:
            current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()
            if not (time(9, 30) <= current_sim_time <= time(11, 30) or time(13, 0) <= current_sim_time):
                continue

            print(f"\n{'=' * 25} 模拟时间点: {current_sim_time} {'=' * 25}")

            # 1. 【已修复】加载并标准化当前时间点的板块和个股数据
            latest_industry_file = find_latest_file(industry_files, current_sim_time)
            latest_concept_file = find_latest_file(concept_files, current_sim_time)
            latest_stock_file = find_latest_file(stock_flow_files, current_sim_time)

            all_sectors_list = []
            if latest_industry_file:
                try:
                    df_ind = pd.read_csv(os.path.join(data_dir, latest_industry_file), encoding='utf-8-sig',
                                         on_bad_lines='skip')
                    if '名称' in df_ind.columns and '今日主力净流入-净额' in df_ind.columns:
                        df_ind['type'] = '行业'
                        all_sectors_list.append(df_ind[['名称', '今日主力净流入-净额', 'type']])
                except Exception as e:
                    print(f"读取行业文件失败: {e}")

            if latest_concept_file:
                try:
                    df_con = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig',
                                         on_bad_lines='skip')
                    rename_map = {}
                    if '行业' in df_con.columns: rename_map['行业'] = '名称'
                    if '净额' in df_con.columns: rename_map['净额'] = '今日主力净流入-净额'
                    df_con.rename(columns=rename_map, inplace=True)
                    if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
                        df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'].apply(
                            lambda x: convert_to_float(x) * 1e8 if abs(
                                convert_to_float(x)) < 1000 else convert_to_float(x))
                        df_con['type'] = '概念'
                        all_sectors_list.append(df_con[['名称', '今日主力净流入-净额', 'type']])
                except Exception as e:
                    print(f"读取概念文件失败: {e}")

            if not all_sectors_list: continue

            df_all_sectors = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称'])
            df_all_sectors['名称'] = df_all_sectors['名称'].str.strip()
            df_all_sectors.dropna(subset=['今日主力净流入-净额'], inplace=True)

            df_stock = pd.read_csv(os.path.join(data_dir, latest_stock_file),
                                   encoding='utf-8-sig') if latest_stock_file else pd.DataFrame()
            if not df_stock.empty:
                df_stock['代码'] = df_stock['代码'].astype(str).str.zfill(6)

            # 2. 清理过期的断层信号
            expired_sectors = [s for s, data in gap_signal_tracker.items() if
                               datetime.combine(datetime.today(), current_sim_time) - data['time'] > timedelta(
                                   minutes=GAP_SIGNAL_TIMEOUT_MINUTES)]
            for s in expired_sectors: del gap_signal_tracker[s]

            # 3. 检测“资金断层”信号
            gap_result = analyze_funding_gap(df_all_sectors, "板块")
            print(gap_result['report'])
            if gap_result['is_gap'] and gap_result.get('score', 0) >= GAP_SIGNAL_SCORE_THRESHOLD:
                for leader in gap_result['leaders']:
                    if leader not in gap_signal_tracker:
                        print(f"  【信号追踪】: 发现新的资金断层龙头【{leader}】，进入一级准备状态...")
                        gap_signal_tracker[leader] = {'time': datetime.combine(datetime.today(), current_sim_time),
                                                      'triggered': False}

            # 4. 检测“资金加速度”信号
            df_previous = pd.DataFrame.from_dict(previous_data_snapshot, orient='index').reset_index().rename(
                columns={'index': '名称'}) if previous_data_snapshot else pd.DataFrame()
            acceleration_signals = analyze_acceleration(df_all_sectors, df_previous)
            if acceleration_signals:
                print("【▲▲▲▲▲ 资金加速度警报! ▲▲▲▲▲】")
                for item in acceleration_signals:
                    print(
                        f"  板块: 【{item['名称']}】资金从 {format_amount(item['今日主力净流入-净额_prev'])} 猛增至 {format_amount(item['今日主力净流入-净额_curr'])}")

            # 5. “三位一体”猎杀模型触发判断
            for acc_signal in acceleration_signals:
                sector_name = acc_signal['名称']
                if sector_name in gap_signal_tracker and not gap_signal_tracker[sector_name].get('triggered', False):
                    print(f"\n【★★★ “三位一体”信号触发! ★★★】")
                    print(f"  触发板块: 【{sector_name}】 (断层信号 + 加速信号共振)")

                    leader_stocks = analyze_sector_internal_flow(sector_name, current_sim_time, data_dir)
                    if not leader_stocks:
                        print(f"  警告: 未能找到板块【{sector_name}】的内部龙头股数据，无法生成买入信号。")
                        continue

                    print(f"  锁定龙头目标: {[s['名称'] for s in leader_stocks]}")
                    for leader_stock in leader_stocks:
                        stock_code = leader_stock['代码']
                        if stock_code not in processed_buy_signals and not df_stock.empty:
                            stock_info_list = df_stock[df_stock['代码'] == stock_code]
                            if not stock_info_list.empty:
                                stock_info = stock_info_list.iloc[0]
                                buy_signal = {
                                    'Time': f"{BACKTEST_DATE} {current_sim_time.strftime('%H:%M:%S')}",
                                    'Stock_Code': stock_code,
                                    'Stock_Name': leader_stock['名称'],
                                    'Price': convert_to_float(stock_info.get('最新价', 0)),
                                    'Change_Percent': convert_to_float(stock_info.get('今日涨跌幅', 0)),
                                    'Main_Flow': convert_to_float(stock_info.get('今日主力净流入-净额', 0)),
                                    'Reason': f"三位一体模型: {sector_name}板块 (断层+加速), 板块内龙头"
                                }
                                all_buy_signals.append(buy_signal)
                                processed_buy_signals.add(stock_code)
                                print(
                                    f"  【BUY SIGNAL】: {buy_signal['Stock_Name']} ({buy_signal['Stock_Code']}) at {buy_signal['Price']:.2f}")

                    gap_signal_tracker[sector_name]['triggered'] = True

            previous_data_snapshot = df_all_sectors.set_index('名称').to_dict('index')

        # --- 复盘结束，生成最终报告 ---
        print("\n" + "=" * 30 + " 复盘总结报告 " + "=" * 30)
        print(f"复盘日期: {date_str}")
        print(f"分析时间点总数: {len(timestamps)}")
        print(f"触发“三位一体”买入信号总数: {len(all_buy_signals)} 个")
        print("=" * 80)

        if not all_buy_signals:
            print("\n--- 复盘完成，未触发任何“三位一体”买入信号 ---")
            return

        if ENABLE_PROFIT_LOSS_CALCULATION:
            print("\n" + "=" * 30 + " 盈亏计算中 " + "=" * 30)
            print("正在获取收盘价并计算当天理论盈亏...")
            pnl_results = batch_calculate_profit_loss(all_buy_signals)
            for i, signal in enumerate(all_buy_signals):
                if i < len(pnl_results):
                    signal['PnL_Display'] = format_profit_loss_display(pnl_results[i])
        else:
            for signal in all_buy_signals:
                signal['PnL_Display'] = "未开启盈亏计算"

        report_title = f"====== {date_str} “三位一体猎杀模型”买入信号报告 ======\n"
        report_body = ""
        for signal in all_buy_signals:
            signal_block = f"""--------------------------------------------------
触发时间: {signal['Time']}
股票信息: {signal['Stock_Name']} ({signal['Stock_Code']})
触发原因: {signal['Reason']}
触发价格: {signal['Price']:.2f} (当时涨跌幅: {signal['Change_Percent']:.2f}%)
主力净流入: {format_amount(signal['Main_Flow'])}
当天理论盈亏: {signal['PnL_Display']}
"""
            report_body += signal_block

        final_report_text = report_title + report_body
        summary_file_path = os.path.join(data_dir, f'hunter_model_buy_summary_{date_str}.txt')
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write(final_report_text)

        print("\n--- 复盘完成 ---")
        print(final_report_text)
        print(f"详细复盘报告已保存至: {summary_file_path}")


if __name__ == "__main__":
    run_hunter_backtest(BACKTEST_DATE)