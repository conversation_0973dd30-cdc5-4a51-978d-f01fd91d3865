# profit_loss_calculator.py
import efinance as ef
import pandas as pd
from datetime import datetime


def get_end_of_day_price(stock_code: str, date_str: str):
    """
    获取指定股票在指定日期的收盘价。
    :param stock_code: 股票代码 (e.g., '600519')
    :param date_str: 日期字符串 (e.g., '2025-07-25')
    :return: 收盘价 (float) or None
    """
    try:
        formatted_date = date_str.replace('-', '')
        df_k = ef.stock.get_quote_history(stock_code, klt=101, end=formatted_date)
        if df_k.empty:
            print(f"⚠️ 未获取到 {stock_code} 在 {date_str} 的行情数据。")
            return None

        target_price = df_k[df_k['日期'] == date_str]['收盘']
        if not target_price.empty:
            return target_price.iloc[0]
        else:
            print(f"⚠️ 在返回的数据中未找到 {date_str} 的确切收盘价，将使用最近一天的收盘价。")
            return df_k.iloc[-1]['收盘']

    except Exception as e:
        print(f"获取 {stock_code} 收盘价时发生错误: {e}")
        return None


def calculate_profit_loss(buy_signal: dict):
    """
    计算单个买入信号的当日盈亏。
    """
    stock_code = buy_signal['Stock_Code']
    buy_price = buy_signal['Price']
    buy_time_str = buy_signal['Time']

    date_str = buy_time_str.split(' ')[0]

    end_price = get_end_of_day_price(stock_code, date_str)

    if end_price is None or buy_price == 0:
        return {
            'stock_code': stock_code,
            'stock_name': buy_signal['Stock_Name'],
            'buy_price': buy_price,
            'end_price': 'N/A',
            'pnl_percent': 'N/A',
            'status': '计算失败'
        }

    pnl_percent = ((end_price - buy_price) / buy_price) * 100

    return {
        'stock_code': stock_code,
        'stock_name': buy_signal['Stock_Name'],
        'buy_price': buy_price,
        'end_price': end_price,
        'pnl_percent': pnl_percent,
        'status': '计算成功'
    }


def batch_calculate_profit_loss(buy_signals: list, **kwargs):
    """
    批量计算买入信号的盈亏。
    """
    results = []
    total = len(buy_signals)
    for i, signal in enumerate(buy_signals):
        print(f"  正在计算盈亏 ({i + 1}/{total}): {signal['Stock_Name']} ({signal['Stock_Code']})...")
        results.append(calculate_profit_loss(signal))
    return results


def format_profit_loss_display(pnl_info: dict):
    """格式化盈亏信息用于报告显示"""
    if pnl_info['status'] != '计算成功' or not isinstance(pnl_info['pnl_percent'], float):
        return pnl_info.get('status', '计算失败')

    pnl_percent = pnl_info['pnl_percent']

    if pnl_percent > 0:
        symbol = '▲'
        color_prefix = "\033[91m"  # 红色
    elif pnl_percent < 0:
        symbol = '▼'
        color_prefix = "\033[92m"  # 绿色
    else:
        symbol = ''
        color_prefix = "\033[0m"  # 默认颜色

    color_suffix = "\033[0m"

    return (f"{color_prefix}{symbol} {pnl_percent:.2f}%{color_suffix} "
            f"(买入: {pnl_info['buy_price']:.2f}, 收盘: {pnl_info['end_price']:.2f})")